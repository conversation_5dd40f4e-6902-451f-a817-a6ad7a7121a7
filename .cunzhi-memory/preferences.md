# 用户偏好设置

- 用户需要我协助进行新功能开发和系统维护工作。当前用户正在查看PcHomeController.java文件，这是PC端首页控制器，负责处理PC端首页相关的API接口。用户希望我记录项目信息以便后续开发协助。
- 用户明确要求房型新增流程改为两步：第一步填写房型基本信息，确定按钮改为"下一步"；第二步填写价格策略（工作日、周末、节假日三种固定策略类型，不使用下拉选择），房型数据需要从第一步带过来。用户不希望后端写死价格倍数，要求商户自己填写具体价格。
- 用户选择学习 Framelink Figma 快速上手路径，需要30分钟的安装和基本使用指导
- 用户明确要求不要生成总结性Markdown文档，不要生成测试脚本，不要编译，用户自己编译，不要运行，用户自己运行
- 用户已配置Figma MCP服务，API密钥为*********************************************，使用figma-developer-mcp包，但服务未正确加载到当前会话中
- 用户已成功配置Figma MCP服务，新的API密钥为*********************************************，服务已正确加载到MCP系统中
- 用户明确要求：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ❌不要运行，用户自己运行
- 用户要求在首页实现完整的自定义日历组件，按照设计图实现日期选择器功能，包含入住/离店日期选择、满房状态显示、月份切换等功能
- 用户要求按照设计图完善酒店详情页，提供了3张设计图作为参考，需要严格按照设计图实现界面布局和功能
- 用户明确要求：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ❌不要运行，用户自己运行
- 用户明确要求：❌不要生成总结性Markdown文档 ❌不要生成测试脚本 ❌不要编译，用户自己编译 ❌不要运行，用户自己运行
- 用户明确要求为特价房源和周末不加价功能生成总结性Markdown文档，这是特殊情况下的例外要求
