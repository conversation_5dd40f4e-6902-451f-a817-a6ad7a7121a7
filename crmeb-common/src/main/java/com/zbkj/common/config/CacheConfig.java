package com.zbkj.common.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Spring Cache + Redis 缓存配置
 * 用于酒店业务的高性能缓存
 * 
 * <AUTHOR> Team
 */
@Configuration
@EnableCaching
public class CacheConfig {

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    /**
     * 配置缓存管理器
     */
    @Bean
    public CacheManager cacheManager() {
        // 配置序列化器
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = jackson2JsonRedisSerializer();

        // 默认缓存配置
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30)) // 默认30分钟过期
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(stringRedisSerializer))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(jackson2JsonRedisSerializer))
                .disableCachingNullValues(); // 不缓存null值

        // 针对不同业务设置不同的缓存时间
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();

        /*
         // 商户信息缓存：1小时（变更频率低）
        cacheConfigurations.put("hotel:merchant", defaultConfig.entryTtl(Duration.ofHours(1)));

        // 房型详情缓存：30分钟（变更频率中等）
        cacheConfigurations.put("hotel:room:details", defaultConfig.entryTtl(Duration.ofMinutes(30)));

        // 房型列表查询缓存：10分钟（包含价格，需要相对实时）
        cacheConfigurations.put("hotel:room:list", defaultConfig.entryTtl(Duration.ofMinutes(10)));
         */
        // 商户信息缓存：30分钟（变更频率低）
        cacheConfigurations.put("hotel:merchant", defaultConfig.entryTtl(Duration.ofMinutes(30)));

        // 房型详情缓存：30秒（包含价格信息，需要高实时性）
        cacheConfigurations.put("hotel:room:details", defaultConfig.entryTtl(Duration.ofSeconds(30)));

        // 房型列表查询缓存：30秒（包含价格，需要最高实时性）
        cacheConfigurations.put("hotel:room:list", defaultConfig.entryTtl(Duration.ofSeconds(30)));
        
        // 酒店列表缓存：15分钟
        cacheConfigurations.put("hotel:list", defaultConfig.entryTtl(Duration.ofMinutes(15)));

        // 系统字典缓存：2小时（变更频率极低）
        cacheConfigurations.put("sysDict", defaultConfig.entryTtl(Duration.ofHours(2)));

        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }

    /**
     * 配置JSON序列化器（修复SubList序列化问题）
     */
    private Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer() {
        Jackson2JsonRedisSerializer<Object> jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer<>(Object.class);
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);

        // 禁用默认类型信息，避免SubList等内部类序列化问题
        objectMapper.deactivateDefaultTyping();

        // 忽略未知属性
        objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        jackson2JsonRedisSerializer.setObjectMapper(objectMapper);
        return jackson2JsonRedisSerializer;
    }
}
