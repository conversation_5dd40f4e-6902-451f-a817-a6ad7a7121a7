import Layout from '@/layout';

const foundationRouter = {
    path: '/foundation',
    component: Layout,
    redirect: '/foundation',
    name: 'Foundation',
    meta: {
        title: '基础维护',
        icon: 'clipboard',
    },
    children: [{
            path: 'housekeeperMgmt',
            component: () =>
                import ('@/views/foundation/housekeeperMgmt/index'),
            name: 'HousekeeperMgmt',
            meta: { title: '小管家配置', icon: '' },
        },
        {
            path: 'data',
            component: () =>
                import ('@/views/foundation/data/index'),
            name: 'Data',
            meta: { title: '基础数据维护', icon: '' },
        },
        {
            path: 'dict',
            component: () =>
                import ('@/views/foundation/dict'),
            name: 'dict',
            meta: { title: '字典维护', icon: '' },
        },
    ],
}

export default foundationRouter;