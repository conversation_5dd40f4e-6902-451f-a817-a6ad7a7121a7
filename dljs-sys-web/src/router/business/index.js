import Layout from '@/layout';

const businessRouter = {
    path: '/business',
    component: Layout,
    redirect: '/business/travelGuideMgmt',
    name: 'Business',
    meta: {
        title: '业务数据',
        icon: 'clipboard',
    },
    children: [{
            path: 'travelGuideMgmt',
            component: () =>
                import ('@/views/business/travelGuideMgmt/index.vue'),
            name: 'TravelGuideMgmt',
            meta: { title: '旅游攻略', icon: '' },
        },
        {
            path: 'noticeMgmt',
            component: () =>
                import ('@/views/business/noticeMgmt/index.vue'),
            name: 'NoticeMgmt',
            meta: { title: '公告管理', icon: '' },
        },
        {
            path: 'informationMgmt',
            component: () =>
                import ('@/views/business/informationMgmt/index.vue'),
            name: 'InformationMgmt',
            meta: { title: '资讯管理', icon: '' },
        },
        {
            path: 'complaintsMgmt',
            component: () =>
                import ('@/views/business/complaintsMgmt/index.vue'),
            name: 'ComplaintsMgmt',
            meta: { title: '投诉与建议', icon: '' },
        },
        {
            path: 'earlyWarningMgmt',
            component: () =>
                import ('@/views/business/earlyWarningMgmt/index.vue'),
            name: 'EarlyWarningMgmt',
            meta: { title: '预警管理', icon: '' },
        },
        {
            path: 'emergencyRescueMgmt',
            component: () =>
                import ('@/views/business/emergencyRescueMgmt/index.vue'),
            name: 'EmergencyRescueMgmt',
            meta: { title: '应急救援管理', icon: '' },
        },
        {
            path: 'integralConfigMgmt',
            component: () =>
                import ('@/views/business/integralConfigMgmt/index.vue'),
            name: 'IntegralConfigMgmt',
            meta: { title: '龙宫币配置', icon: '' },
        },
        {
            path: 'allotRecord',
            component: () =>
                import ('@/views/business/allotRecord/index.vue'),
            name: 'AllotRecord',
            meta: { title: '龙宫币分配记录', icon: '' },
        },
        {
            path: 'receiveRecord',
            component: () =>
                import ('@/views/business/receiveRecord/index.vue'),
            name: 'ReceiveRecord',
            meta: { title: '龙宫币获取记录', icon: '' },
        },
        {
          path: 'verificationRecord',
          component: () =>
            import ('@/views/business/verificationRecord/index.vue'),
          name: 'VerificationRecord',
          meta: { title: '龙宫币核销记录', icon: '' },
        },
        {
          path: 'questionnaire',
          component: () =>
              import ('@/views/business/questionnaire/index.vue'),
          name: 'Questionnaire',
          meta: { title: '问卷调查', icon: '' },
        },
        {
          path: 'hotspot',
          component: () =>
              import ('@/views/business/hotspot/index.vue'),
          name: 'Hotspot',
          meta: { title: '热点配置', icon: '' },
        },
    ]
}

export default businessRouter;
