// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from '@/layout';

const distributionRouter = {
  path: '/distributionmanagement',
  component: Layout,
  redirect: '/distributionmanagement/distributionconfig',
  name: 'DistributionManagement',
  meta: {
    title: '分销管理',
    icon: 'clipboard',
  },
  children: [
    {
      path: 'index',
      component: () => import('@/views/distributionmanagement/index'),
      name: 'distributionIndex',
      meta: { title: '分销员管理', icon: '' },
    },
    {
      path: 'distributionconfig',
      component: () => import('@/views/distributionmanagement/config/index'),
      name: 'distributionConfig',
      meta: { title: '分销配置', icon: '' },
    },
    {
      path: 'distributionapplication',
      component: () => import('@/views/distributionmanagement/application/index'),
      name: 'distributionApplication',
      meta: { title: '分销申请', icon: '' },
    },
  ],
};

export default distributionRouter;
