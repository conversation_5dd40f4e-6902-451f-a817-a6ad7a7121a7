<template>
    <div class="map-container">
      <div class="map-wrapper">
        <div id="map-container" style="width: 100%; height: 300px"></div>
        <div class="map-tools">
          <el-button type="primary" @click="confirmLocation">确认</el-button>
          <el-button @click="mapClose">取消</el-button>
        </div>
      </div>
      <div class="location-info">
        <el-form label-width="80px">
          <el-form-item label="经度">
            <el-input v-model="longitude" placeholder="请选择位置"></el-input>
          </el-form-item>
          <el-form-item label="纬度">
            <el-input v-model="latitude" placeholder="请选择位置"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </template>

  <script>
  export default {
    name: 'TencentMapPicker',
    props: {
      visible: {
        type: Boolean,
        default: false
      },
      defaultLng: {
        type: Number,
        default: 100.257298
      },
      defaultLat: {
        type: Number,
        default: 25.695042
      }
    },
    data() {
      return {
        dialogVisible: this.visible,
        map: null,
        marker: null,
        longitude: '',
        latitude: '',
        address: ''
      }
    },
    // watch: {
    //   visible(newVal) {
    //     this.dialogVisible = newVal
    //     if (newVal) {
    //       this.$nextTick(() => {
    //         this.initMap()
    //       })
    //     }
    //   },
    //   dialogVisible(newVal) {
    //     this.$emit('update:visible', newVal)
    //     if (!newVal) {
    //       this.clearMap()
    //     }
    //   }
    // },
    mounted() {
      this.clearMap();
      this.initMap();
    },
    methods: {
      initMap() {
        console.log('initMap ++++++++')
        // 初始化地图
        this.map = new TMap.Map(document.getElementById('map-container'), {
          center: new TMap.LatLng(this.defaultLat, this.defaultLng),
          zoom: 13
        })

        // 添加点击事件
        this.map.on('click', this.handleMapClick)

        // 初始化标记点
        this.addMarker(this.defaultLng, this.defaultLat)
      },

      addMarker(lng, lat) {
        // 清除现有标记
        if (this.marker) {
          this.marker.setMap(null)
        }

        // 创建新标记
        this.marker = new TMap.MultiMarker({
          map: this.map,
          geometries: [{
            position: new TMap.LatLng(lat, lng),
            id: 'marker'
          }]
        })

        // 移动地图中心到标记点
        this.map.setCenter(new TMap.LatLng(lat, lng))
      },

      handleMapClick(evt) {
        const { lat, lng } = evt.latLng
        this.longitude = lng.toFixed(6)
        this.latitude = lat.toFixed(6)

        // 添加新标记
        this.addMarker(lng, lat)

        // 添加点击事件
        this.map.on('click', this.handleMapClick)

        // 逆地址解析
        // this.reverseGeocoder(lat, lng)

      },

      reverseGeocoder(lat, lng) {
        const geocoder = new TMap.Geocoder({
          complete: (result) => {
            if (result.detail && result.detail.address) {
              this.address = result.detail.address
            }
          }
        })

        geocoder.reverseGeocoder({
          location: {
            lat,
            lng
          }
        })
      },

      confirmLocation() {
        if (!this.longitude || !this.latitude) {
          this.$message.warning('请先在地图上选择位置')
          return
        }

        this.$emit('confirm', {
          lng: this.longitude,
          lat: this.latitude,
          address: this.address
        })
        this.dialogVisible = false
      },

      clearMap() {
        if (this.map) {
          this.map.off('click', this.handleMapClick)
          this.map.destroy()
          this.map = null
        }
        if (this.marker) {
          this.marker.setMap(null)
          this.marker = null
        }
      },

      handleClose() {
        this.clearMap()
      },

      mapClose() {
        this.$emit('openMap', false);
      }
    }
  }
  </script>

<style scoped>
  .map-container {
    position: relative;
    height: 520px;
  }

  .map-wrapper {
    margin-bottom: 20px;
  }

  .map-tools {
    margin-top: 10px;
    text-align: center;
  }

  .location-info {
    padding: 10px;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

</style>
