{"data": {"list": [{"id": 1, "cate_id": 5, "type": 1, "name": "商城首页", "url": "/pages/index/index", "param": " ", "example": "/pages/index/index", "status": 1, "sort": 999, "add_time": 1626837579}, {"id": 2, "cate_id": 5, "type": 1, "name": "商城分类", "url": "/pages/goods_cate/index", "param": " ", "example": "/pages/goods_cate/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 3, "cate_id": 5, "type": 1, "name": "逛逛", "url": "/pages/discover_index/index", "param": " ", "example": "/pages/discover_index/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 28, "cate_id": 5, "type": 1, "name": "店铺街", "url": "/pages/merchant/merchant_street/index", "param": " ", "example": "/pages/merchant/merchant_street/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 4, "cate_id": 5, "type": 1, "name": "购物车", "url": "/pages/order_addcart/order_addcart", "param": " ", "example": "/pages/order_addcart/order_addcart", "status": 1, "sort": 997, "add_time": 1626837579}, {"id": 5, "cate_id": 5, "type": 1, "name": "分类商品列表", "url": "/pages/goods/goods_list/index", "param": "sid=1&title=测试分类名称", "example": "/pages/goods_list/index?sid=1&title=测试分类名称", "status": 1, "sort": 996, "add_time": 1626837579}, {"id": 6, "cate_id": 5, "type": 3, "name": "充值页面", "url": "/pages/users/user_payment/index", "param": " ", "example": "/pages/users/user_payment/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 7, "cate_id": 5, "type": 3, "name": "佣金排行", "url": "/pages/users/commission_rank/index", "param": " ", "example": "/pages/users/commission_rank/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 8, "cate_id": 5, "type": 3, "name": "推广人排行", "url": "/pages/users/promoter_rank/index", "param": " ", "example": "/pages/users/promoter_rank/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 9, "cate_id": 5, "type": 3, "name": "推广人订单", "url": "/pages/users/promoter-order/index", "param": " ", "example": "/pages/users/promoter-order/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 10, "cate_id": 5, "type": 2, "name": "推广人列表", "url": "/pages/users/promoter-list/index", "param": " ", "example": "/pages/users/promoter-list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 11, "cate_id": 5, "type": 2, "name": "分销海报", "url": "/pages/users/user_spread_code/index", "param": " ", "example": "/pages/users/user_spread_code/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 12, "cate_id": 5, "type": 3, "name": "提现页面", "url": "/pages/users/user_transferOut/index", "param": " ", "example": "/pages/users/user_transferOut/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 13, "cate_id": 5, "type": 2, "name": "我的推广", "url": "/pages/users/user_spread_user/index", "param": " ", "example": "/pages/users/user_spread_user/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 14, "cate_id": 5, "type": 1, "name": "退款列表", "url": "/pages/goods/user_return_list/index", "param": " ", "example": "/pages/goods/user_return_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 15, "cate_id": 5, "type": 1, "name": "我的订单", "url": "/pages/goods/order_list/index", "param": " ", "example": "/pages/goods/order_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 16, "cate_id": 5, "type": 3, "name": "个人资料", "url": "/pages/users/user_info/index", "param": " ", "example": "/pages/users/user_info/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 17, "cate_id": 5, "type": 3, "name": "我的账户", "url": "/pages/users/user_money/index", "param": " ", "example": "/pages/users/user_money/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 18, "cate_id": 5, "type": 3, "name": "地址列表", "url": "/pages/address/user_address_list/index", "param": " ", "example": "/pages/address/user_address_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 19, "cate_id": 5, "type": 3, "name": "收藏页面", "url": "/pages/goods/user_goods_collection/index", "param": " ", "example": "/pages/goods/user_goods_collection/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 20, "cate_id": 5, "type": 3, "name": "签到页面", "url": "/pages/merchant/user_sgin/index", "param": " ", "example": "/pages/merchant/user_sgin/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 21, "cate_id": 5, "type": 1, "name": "文章列表", "url": "/pages/goods/news_list/index", "param": "id=文章ID", "example": "/pages/goods/news_list/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 22, "cate_id": 5, "type": 1, "name": "商户入驻", "url": "/pages/users/settled/index", "param": " ", "example": "/pages/users/settled/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 23, "cate_id": 5, "type": 1, "name": "我的关注", "url": "/pages/goods/user_merchant_collection/index", "param": " ", "example": "/pages/goods/user_merchant_collection/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 24, "cate_id": 5, "type": 1, "name": "申请记录", "url": "/pages/merchant/application_record/index", "param": " ", "example": "/pages/merchant/application_record/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 25, "cate_id": 5, "type": 3, "name": "个人中心", "url": "/pages/user/index", "param": " ", "example": "/pages/user/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 26, "cate_id": 5, "type": 3, "name": "佣金记录", "url": "/pages/users/user_spread_money/index?type=2", "param": " ", "example": "/pages/users/user_spread_money/index?type=2", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 27, "cate_id": 5, "type": 3, "name": "提现记录", "url": "/pages/users/user_spread_money/index?type=1", "param": " ", "example": "/pages/users/user_spread_money/index?type=1", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 29, "cate_id": 6, "type": 3, "name": "领券中心", "url": "/pages/activity/couponList/index", "param": "", "example": "/pages/activity/couponList/index", "status": 1, "sort": 0, "add_time": 1626837579}, {"id": 30, "cate_id": 5, "type": 3, "name": "我的等级", "url": "/pages/goods/user_grade/index", "param": " ", "example": "/pages/goods/user_grade/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 31, "cate_id": 5, "type": 1, "name": "付费会员", "url": "/pages/activity/vip_paid/index", "param": " ", "example": "/pages/activity/vip_paid/index", "status": 1, "sort": 998, "add_time": 1626837579}, {"id": 30, "cate_id": 5, "type": 1, "name": "酒店预订", "url": "/pages/shopping/hotel/index", "param": " ", "example": "/pages/shopping/hotel/index", "status": 1, "sort": 999, "add_time": 1626837579}]}}