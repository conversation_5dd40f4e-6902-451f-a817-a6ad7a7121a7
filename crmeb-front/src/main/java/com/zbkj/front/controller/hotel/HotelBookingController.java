package com.zbkj.front.controller.hotel;

import com.github.pagehelper.PageInfo;
import com.zbkj.common.page.CommonPage;
import com.zbkj.common.request.PageParamRequest;
import com.zbkj.common.request.hotel.HotelRoomListRequest;
import com.zbkj.common.request.hotel.HotelSearchRequest;
import com.zbkj.common.response.hotel.HotelListResponse;
import com.zbkj.common.response.hotel.HotelRoomListResponse;
import com.zbkj.common.result.CommonResult;
import com.zbkj.service.service.hotel.HotelBookingService;
import com.zbkj.service.service.hotel.HotelCancelRuleService;
import com.zbkj.service.service.hotel.HotelProductSyncService;
import com.zbkj.service.service.OrderService;
import com.zbkj.service.service.OrderDetailService;
import com.zbkj.common.model.order.Order;
import com.zbkj.common.model.order.OrderDetail;
import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 酒店预订控制器 - 用户端
 *
 * <AUTHOR> Team
 * @since 2025-01-17
 */
@Slf4j
@RestController
@RequestMapping("api/front/hotel")
@Api(tags = "用户端 - 酒店预订")
@Validated
public class HotelBookingController {

    @Autowired
    private HotelBookingService hotelBookingService;

    @Autowired
    private HotelCancelRuleService hotelCancelRuleService;

    @Autowired
    private HotelProductSyncService hotelProductSyncService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderDetailService orderDetailService;

    /**
     * 酒店列表查询
     */
    @ApiOperation(value = "酒店列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<HotelListResponse>> getHotelList(
            @ModelAttribute @Validated HotelSearchRequest request,
            @ModelAttribute @Validated PageParamRequest pageParamRequest) {
        
        PageInfo<HotelListResponse> pageInfo = hotelBookingService.getHotelList(request, pageParamRequest);
        return CommonResult.success(CommonPage.restPage(pageInfo));
    }

    /**
     * 酒店详情
     */
    @ApiOperation(value = "酒店详情")
    @RequestMapping(value = "/detail/{hotelId}", method = RequestMethod.GET)
    public CommonResult<HotelListResponse> getHotelDetail(
            @PathVariable @NotNull(message = "酒店ID不能为空") Integer hotelId) {
        
        HotelListResponse hotel = hotelBookingService.getHotelDetail(hotelId);
        return CommonResult.success(hotel);
    }

    /**
     * 房型列表查询
     */
    @ApiOperation(value = "房型列表查询")
    @RequestMapping(value = "/room/list", method = RequestMethod.GET)
    public CommonResult<List<HotelRoomListResponse>> getRoomList(
            @ModelAttribute @Validated HotelRoomListRequest request) {
        
        List<HotelRoomListResponse> roomList = hotelBookingService.getRoomList(request);
        return CommonResult.success(roomList);
    }

    /**
     * 解析商品名称（调试用）
     */
    @ApiOperation(value = "解析商品名称")
    @RequestMapping(value = "/parse/product", method = RequestMethod.GET)
    public CommonResult<HotelBookingService.HotelProductInfo> parseProductName(
            @RequestParam @NotNull(message = "商品名称不能为空") String productName) {

        HotelBookingService.HotelProductInfo info = hotelBookingService.parseProductName(productName);
        return CommonResult.success(info);
    }

    /**
     * 检查孤立的酒店商品数据（调试用）
     */
    @ApiOperation(value = "检查孤立商品数据")
    @RequestMapping(value = "/debug/checkOrphanedProducts", method = RequestMethod.GET)
    public CommonResult<String> checkOrphanedProducts() {
        try {
            hotelBookingService.checkOrphanedHotelProducts();
            return CommonResult.success("检查完成，请查看日志");
        } catch (Exception e) {
            log.error("检查孤立商品数据失败", e);
            return CommonResult.failed("检查失败: " + e.getMessage());
        }
    }

    /**
     * 清理孤立的酒店商品数据（调试用）
     */
    @ApiOperation(value = "清理孤立商品数据")
    @RequestMapping(value = "/debug/cleanOrphanedProducts", method = RequestMethod.POST)
    public CommonResult<String> cleanOrphanedProducts() {
        try {
            hotelProductSyncService.cleanOrphanedHotelProducts();
            return CommonResult.success("清理完成，请查看日志");
        } catch (Exception e) {
            log.error("清理孤立商品数据失败", e);
            return CommonResult.failed("清理失败: " + e.getMessage());
        }
    }

    /**
     * 手动清除酒店缓存（调试用）
     */
    @ApiOperation(value = "清除酒店缓存")
    @RequestMapping(value = "/debug/clearCache", method = RequestMethod.POST)
    public CommonResult<String> clearHotelCache() {
        try {
            hotelBookingService.clearAllHotelCache();
            return CommonResult.success("缓存清除成功");
        } catch (Exception e) {
            log.error("清除缓存失败", e);
            return CommonResult.failed("清除缓存失败: " + e.getMessage());
        }
    }

    /**
     * 计算酒店退款金额
     * 根据酒店取消规则和提前取消时间计算退款金额
     */
    @ApiOperation(value = "计算酒店退款金额")
    @RequestMapping(value = "/calculate-refund", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> calculateHotelRefundAmount(
            @RequestParam @NotNull(message = "订单号不能为空") String orderNo,
            @RequestParam @NotNull(message = "订单详情ID不能为空") Integer orderDetailId) {

        try {
            // 1. 参数校验与数据获取
            Order order = validateAndGetOrder(orderNo);
            List<OrderDetail> orderDetailList = validateAndGetOrderDetails(orderNo);

            // 2. 业务逻辑校验
            validateHotelOrder(orderDetailList);

            // 3. 计算退款金额
            return CommonResult.success(calculateRefundResult(order, orderDetailList));

        } catch (Exception e) {
            log.error("计算酒店退款金额失败，订单号：{}", orderNo, e);
            return CommonResult.failed("计算退款金额失败：" + e.getMessage());
        }
    }

    /**
     * 校验并获取订单信息
     */
    private Order validateAndGetOrder(String orderNo) {
        Order order = orderService.getByOrderNo(orderNo);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }
        return order;
    }

    /**
     * 校验并获取订单详情列表
     */
    private List<OrderDetail> validateAndGetOrderDetails(String orderNo) {
        List<OrderDetail> orderDetailList = orderDetailService.getByOrderNo(orderNo);
        if (CollUtil.isEmpty(orderDetailList)) {
            throw new RuntimeException("订单详情不存在");
        }
        return orderDetailList;
    }

    /**
     * 校验是否为酒店订单
     */
    private void validateHotelOrder(List<OrderDetail> orderDetailList) {
        OrderDetail firstOrderDetail = orderDetailList.get(0);
        if (!Integer.valueOf(7).equals(firstOrderDetail.getProductType())) {
            throw new RuntimeException("非酒店商品不支持此退款计算");
        }
    }

    /**
     * 计算退款结果
     */
    private Map<String, Object> calculateRefundResult(Order order, List<OrderDetail> orderDetailList) {
        // 1. 计算订单总支付金额
        BigDecimal totalPayPrice = calculateTotalPayPrice(orderDetailList);

        // 2. 获取入住日期和提前取消小时数
        String checkInDate = getCheckInDate(orderDetailList.get(0));
        long advanceHours = calculateAdvanceHours(checkInDate);

        // 3. 处理过期订单
        if (advanceHours < 0) {
            return buildExpiredOrderResult(totalPayPrice);
        }

        // 4. 计算取消费用
        BigDecimal cancelFee = hotelCancelRuleService.calculateCancelFee(
                totalPayPrice, (int) advanceHours, order.getMerId());

        // 5. 构建返回结果
        return buildRefundResult(totalPayPrice, cancelFee, advanceHours, checkInDate);
    }

    /**
     * 计算订单总支付金额
     */
    private BigDecimal calculateTotalPayPrice(List<OrderDetail> orderDetailList) {
        return orderDetailList.stream()
                .map(OrderDetail::getPayPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取入住日期
     */
    private String getCheckInDate(OrderDetail orderDetail) {
        HotelBookingService.HotelProductInfo productInfo =
                hotelBookingService.parseProductName(orderDetail.getProductName());
        if (productInfo == null || productInfo.getCheckInDate() == null) {
            throw new RuntimeException("无法解析酒店商品信息");
        }
        return productInfo.getCheckInDate();
    }

    /**
     * 计算提前取消小时数
     */
    private long calculateAdvanceHours(String checkInDate) {
        try {
            LocalDateTime checkInDateTime = LocalDateTime.parse(checkInDate + "T14:00:00");
            return ChronoUnit.HOURS.between(LocalDateTime.now(), checkInDateTime);
        } catch (Exception e) {
            throw new RuntimeException("入住日期格式错误");
        }
    }

    /**
     * 构建过期订单结果
     */
    private Map<String, Object> buildExpiredOrderResult(BigDecimal totalPayPrice) {
        Map<String, Object> result = new HashMap<>();
        result.put("canCancel", false);
        result.put("refundAmount", BigDecimal.ZERO);
        result.put("cancelFee", totalPayPrice);
        result.put("message", "已过入住时间，无法申请退款");
        return result;
    }

    /**
     * 构建退款结果
     */
    private Map<String, Object> buildRefundResult(BigDecimal totalPayPrice, BigDecimal cancelFee,
                                                  long advanceHours, String checkInDate) {
        Map<String, Object> result = new HashMap<>();

        // 检查是否不可取消(-1表示不可取消)
        if (cancelFee.compareTo(new BigDecimal("-1")) == 0) {
            result.put("canCancel", false);
            result.put("refundAmount", BigDecimal.ZERO);
            result.put("cancelFee", totalPayPrice);
            result.put("message", "根据取消规则，当前时间不允许取消订单");
        } else {
            // 计算退款金额 = 订单总金额 - 取消费用
            BigDecimal refundAmount = totalPayPrice.subtract(cancelFee);
            result.put("canCancel", true);
            result.put("refundAmount", refundAmount);
            result.put("cancelFee", cancelFee);
            result.put("advanceHours", advanceHours);
            result.put("checkInDate", checkInDate);
            result.put("totalPayPrice", totalPayPrice);

            String message = cancelFee.compareTo(BigDecimal.ZERO) == 0 ?
                    "免费取消" : "扣除取消费用：¥" + cancelFee;
            result.put("message", message);
        }

        return result;
    }
}
