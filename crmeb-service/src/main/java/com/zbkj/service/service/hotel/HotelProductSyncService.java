package com.zbkj.service.service.hotel;

import java.math.BigDecimal;

/**
 * 酒店商品同步服务接口
 * 
 * 功能说明：
 * 1. 负责将酒店房间信息同步到商品表
 * 2. 处理价格策略计算和商品生成
 * 3. 管理商品分类和图片处理
 * 4. 支持定时任务和手动同步
 * 
 * <AUTHOR> Team
 * @since 2025-01-17
 */
public interface HotelProductSyncService {

    /**
     * 同步所有酒店商品
     * 遍历所有启用的酒店房间，生成对应的商品
     */
    void syncAllHotelProducts();

    /**
     * 同步指定商户的酒店商品
     * 
     * @param merchantId 商户ID
     */
    void syncHotelProductsByMerchant(Integer merchantId);

    /**
     * 同步指定房间的商品
     * 
     * @param roomId 房间ID
     */
    void syncHotelProductsByRoom(Integer roomId);

    /**
     * 清理过期的酒店商品
     * 删除超过指定天数且无订单关联的过期商品
     */
    void cleanExpiredHotelProducts();


    /**
     * 清理孤立的酒店商品
     * 删除没有对应房型的商品数据
     */
    void cleanOrphanedHotelProducts();


    /**
     * 批量更新酒店商品价格
     * 当价格策略变更时，更新相关商品价格
     * 
     * @param roomId 房间ID
     */
    void updateHotelProductPrices(Integer roomId);

}
