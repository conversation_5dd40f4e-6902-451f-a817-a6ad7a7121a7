# Java 后端开发规范指南

作为经验丰富的 Java 开发者，在后端接口开发中注重性能、代码质量和数据库优化是非常重要的。以下是你在开发业务模块接口时需要遵循的关键要点：

## 1. 代码风格和命名规范

### 命名规范
- **类名**：使用 PascalCase（UserService、OrderController）
- **方法名**：使用 camelCase（getUserById、createOrder）
- **常量**：使用 UPPER_SNAKE_CASE（MAX_RETRY_COUNT、DEFAULT_TIMEOUT）
- **包名**：使用小写字母和点分隔（com.company.module.service）

### 代码组织
- 每个类不超过 500 行代码
- 每个方法不超过 50 行代码
- 合理使用空行分隔逻辑块
- 统一的缩进（4个空格或1个Tab）

```java
// 正例：清晰的命名和结构
public class UserService {
    private static final int MAX_LOGIN_ATTEMPTS = 3;

    public UserDTO getUserById(Long userId) {
        validateUserId(userId);
        User user = userRepository.findById(userId);
        return convertToDTO(user);
    }

    private void validateUserId(Long userId) {
        if (userId == null || userId <= 0) {
            throw new IllegalArgumentException("Invalid user ID");
        }
    }
}
```

## 2. 数据库访问优化
减少数据库交互次数：尽量通过单次查询获取所需数据（JOIN、子查询或批量查询）

合理使用索引：确保查询字段有适当索引，避免全表扫描

避免 N+1 问题：使用 JOIN FETCH（JPA）或批量查询代替循环中的单条查询

分页查询：大数据集必须分页（LIMIT/OFFSET 或游标分页）

禁用 SELECT *：只查询需要的字段

## 3. 设计模式和架构原则

### 常用设计模式
- **单例模式**：Spring Bean 默认单例，注意线程安全
- **工厂模式**：创建复杂对象时使用
- **策略模式**：替代大量 if-else 判断
- **观察者模式**：事件驱动架构

```java
// 策略模式示例：支付方式处理
public interface PaymentStrategy {
    PaymentResult process(PaymentRequest request);
}

@Component
public class PaymentService {
    private final Map<PaymentType, PaymentStrategy> strategies;

    public PaymentResult pay(PaymentRequest request) {
        PaymentStrategy strategy = strategies.get(request.getType());
        return strategy.process(request);
    }
}
```

### SOLID 原则
- **单一职责**：一个类只有一个改变的理由
- **开闭原则**：对扩展开放，对修改关闭
- **里氏替换**：子类可以替换父类
- **接口隔离**：不依赖不需要的接口
- **依赖倒置**：依赖抽象而不是具体实现

## 4. 代码质量规范
单一职责原则：每个方法/类只做一件事

防御性编程：对输入参数进行校验（如 Spring 的 @Valid）

异常处理：区分业务异常（自定义异常）和系统异常

日志规范：关键流程打日志（INFO），错误打 ERROR 并带上上下文

避免重复代码：提取公共方法或使用工具类

## 5. 单元测试规范

### 测试覆盖率
- 核心业务逻辑覆盖率 ≥ 80%
- 公共工具类覆盖率 ≥ 90%
- 使用 JUnit 5 + Mockito

### 测试原则
- **AAA 模式**：Arrange（准备）、Act（执行）、Assert（断言）
- **独立性**：测试之间不相互依赖
- **可重复**：多次运行结果一致
- **快速执行**：单个测试 < 1秒

```java
// 正例：清晰的单元测试
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserService userService;

    @Test
    @DisplayName("根据ID获取用户 - 成功场景")
    void getUserById_Success() {
        // Arrange
        Long userId = 1L;
        User mockUser = new User(userId, "张三");
        when(userRepository.findById(userId)).thenReturn(mockUser);

        // Act
        UserDTO result = userService.getUserById(userId);

        // Assert
        assertThat(result.getId()).isEqualTo(userId);
        assertThat(result.getName()).isEqualTo("张三");
    }
}
```

## 6. 安全编程规范

### 输入验证
- 所有外部输入必须验证（参数、文件、网络数据）
- 使用白名单而非黑名单
- 防止 SQL 注入：使用参数化查询
- 防止 XSS：输出编码

```java
// 正例：安全的参数验证
@RestController
public class UserController {

    @PostMapping("/users")
    public ResponseEntity<User> createUser(@Valid @RequestBody CreateUserRequest request) {
        // @Valid 自动验证请求参数
        return ResponseEntity.ok(userService.createUser(request));
    }
}

public class CreateUserRequest {
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{3,20}$", message = "用户名格式不正确")
    private String username;

    @Email(message = "邮箱格式不正确")
    private String email;
}
```

### 敏感信息处理
- 密码使用 BCrypt 等强哈希算法
- 敏感数据不记录到日志
- 使用 HTTPS 传输敏感信息
- 定期更新依赖库版本

## 7. 性能优化要点
缓存策略：

高频读少改的数据用 Redis 缓存

合理设置缓存过期时间

批量操作：

批量插入/更新（batchUpdate）

批量查询（WHERE id IN (...)）

异步处理：

耗时操作走消息队列（如 RabbitMQ/Kafka）

非核心流程可异步化（如日志记录）

## 8. 监控和可观测性

### 日志规范
- **分级记录**：ERROR（错误）、WARN（警告）、INFO（关键流程）、DEBUG（调试信息）
- **结构化日志**：使用 JSON 格式，便于检索
- **上下文信息**：包含用户ID、请求ID、业务标识

```java
// 正例：结构化日志
@Slf4j
@Service
public class OrderService {

    public Order createOrder(CreateOrderRequest request) {
        String requestId = MDC.get("requestId");
        log.info("开始创建订单, requestId={}, userId={}, productId={}",
                requestId, request.getUserId(), request.getProductId());

        try {
            Order order = processOrder(request);
            log.info("订单创建成功, requestId={}, orderId={}", requestId, order.getId());
            return order;
        } catch (Exception e) {
            log.error("订单创建失败, requestId={}, error={}", requestId, e.getMessage(), e);
            throw e;
        }
    }
}
```

### 性能监控
- **响应时间**：接口响应时间 < 500ms
- **吞吐量**：QPS 监控和告警
- **错误率**：错误率 < 1%
- **资源使用**：CPU、内存、数据库连接池

### 健康检查
```java
@Component
public class HealthCheckService {

    @Autowired
    private DataSource dataSource;

    public HealthStatus checkDatabase() {
        try {
            dataSource.getConnection().isValid(5);
            return HealthStatus.UP;
        } catch (Exception e) {
            return HealthStatus.DOWN;
        }
    }
}
```

## 9. 配置管理规范

### 环境配置
- **分环境配置**：dev、test、prod
- **敏感配置**：使用环境变量或配置中心
- **配置热更新**：支持不重启更新配置

```yaml
# application-prod.yml
spring:
  datasource:
    url: ${DB_URL:***********************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD}

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}

app:
  config:
    max-retry-count: ${MAX_RETRY_COUNT:3}
    timeout: ${TIMEOUT:30000}
```

## 10. API 设计原则
RESTful 规范：

资源化 URI（/users/{id}）

正确使用 HTTP Method（GET/POST/PUT/DELETE）

版本控制：API 路径或 Header 带版本号（/v1/users）

响应标准化：

json
{
  "code": 200,
  "message": "success",
  "data": {...},
  "timestamp": **********
}

### 接口文档
- **Swagger/OpenAPI**：自动生成API文档
- **版本管理**：向后兼容，渐进式升级
- **错误码规范**：统一的错误码和错误信息

```java
// 正例：完整的API接口
@RestController
@RequestMapping("/api/v1/users")
@Api(tags = "用户管理")
public class UserController {

    @GetMapping("/{id}")
    @ApiOperation(value = "根据ID获取用户", notes = "返回用户详细信息")
    @ApiResponses({
        @ApiResponse(code = 200, message = "成功"),
        @ApiResponse(code = 404, message = "用户不存在"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public ResponseEntity<ApiResponse<UserDTO>> getUserById(
            @ApiParam(value = "用户ID", required = true) @PathVariable Long id) {

        UserDTO user = userService.getUserById(id);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
}
```

## 11. 并发与线程安全
线程池：避免随意创建线程，使用线程池管理

锁粒度：

分布式锁用 Redis（Redisson）

同步代码块尽量缩小范围

原子操作：利用数据库乐观锁或 CAS

## 12. 依赖管理规范

### Maven/Gradle 最佳实践
- **版本管理**：使用 BOM（Bill of Materials）统一版本
- **依赖冲突**：排除传递依赖冲突
- **安全扫描**：定期检查依赖漏洞

```xml
<!-- Maven 依赖管理示例 -->
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-dependencies</artifactId>
            <version>2.7.0</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>

<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <!-- 版本由 BOM 管理，无需指定 -->
    </dependency>
</dependencies>
```

## 13. 错误处理和恢复策略

### 异常分类
- **业务异常**：可预期的业务错误（用户不存在、余额不足）
- **系统异常**：不可预期的系统错误（网络超时、数据库连接失败）
- **参数异常**：输入参数错误

```java
// 正例：分层异常处理
public class BusinessException extends RuntimeException {
    private final String errorCode;

    public BusinessException(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}

@ControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Void>> handleBusinessException(BusinessException e) {
        return ResponseEntity.badRequest()
                .body(ApiResponse.error(e.getErrorCode(), e.getMessage()));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleSystemException(Exception e) {
        log.error("系统异常", e);
        return ResponseEntity.status(500)
                .body(ApiResponse.error("SYSTEM_ERROR", "系统繁忙，请稍后重试"));
    }
}
```

### 重试机制
```java
@Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
public String callExternalService() {
    // 调用外部服务
    return externalServiceClient.getData();
}
```

## 14. 实际案例对比
反例（问题代码）:

java
// 循环中查询数据库
public List<UserVO> getUsers(List<Integer> ids) {
    return ids.stream()
              .map(id -> {
                  User user = userDao.findById(id); // 每次循环都查库
                  return convertToVO(user);
              })
              .collect(Collectors.toList());
}
正例（优化后）:

java
// 一次性批量查询
public List<UserVO> getUsers(List<Integer> ids) {
    // 1. 单次批量查询
    List<User> users = userDao.findByIdIn(ids); 
    
    // 2. 内存处理
    Map<Integer, User> userMap = users.stream()
                                     .collect(Collectors.toMap(User::getId, Function.identity()));
    
    // 3. 转换VO
    return ids.stream()
              .map(id -> convertToVO(userMap.get(id)))
              .collect(Collectors.toList());
}
```

## 15. 代码审查清单

### 功能性检查
- [ ] 业务逻辑是否正确实现
- [ ] 边界条件是否处理
- [ ] 异常情况是否考虑
- [ ] 性能是否满足要求

### 代码质量检查
- [ ] 命名是否清晰易懂
- [ ] 方法是否过长（>50行）
- [ ] 是否有重复代码
- [ ] 注释是否充分

### 安全性检查
- [ ] 输入验证是否完整
- [ ] 权限控制是否正确
- [ ] 敏感信息是否泄露
- [ ] SQL注入风险是否存在

### 可维护性检查
- [ ] 代码结构是否清晰
- [ ] 依赖关系是否合理
- [ ] 配置是否外部化
- [ ] 日志是否充分

## 16. 性能调优指南

### JVM 调优
```bash
# 生产环境 JVM 参数示例
-Xms2g -Xmx2g
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:+HeapDumpOnOutOfMemoryError
-XX:HeapDumpPath=/logs/heapdump.hprof
```

### 数据库调优
- **索引优化**：为查询字段创建合适索引
- **查询优化**：避免全表扫描，使用 EXPLAIN 分析
- **连接池**：合理配置连接池大小
- **读写分离**：读多写少场景使用主从分离

### 缓存策略
```java
// 多级缓存示例
@Service
public class ProductService {

    @Cacheable(value = "products", key = "#id")
    public Product getProductById(Long id) {
        return productRepository.findById(id);
    }

    @CacheEvict(value = "products", key = "#product.id")
    public Product updateProduct(Product product) {
        return productRepository.save(product);
    }
}
```

## 17. 部署和运维规范

### 容器化部署
```dockerfile
# Dockerfile 示例
FROM openjdk:11-jre-slim

WORKDIR /app
COPY target/app.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 健康检查
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    image: myapp:latest
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
```

### 监控告警
- **应用监控**：使用 Micrometer + Prometheus
- **日志监控**：ELK Stack（Elasticsearch + Logstash + Kibana）
- **链路追踪**：Zipkin 或 Jaeger
- **告警规则**：响应时间、错误率、资源使用率

---

## 总结

遵循以上规范可以帮助你：
1. **提高代码质量**：减少 Bug，提升可维护性
2. **提升系统性能**：优化响应时间和吞吐量
3. **增强系统稳定性**：完善的错误处理和监控
4. **提高开发效率**：标准化的开发流程
5. **降低运维成本**：自动化部署和监控

记住：**好的代码不仅要能运行，还要易读、易维护、高性能、安全可靠**。

